const Room = require('../models/Room');
const Transaction = require('../models/Transaction');
const mongoose = require('mongoose');

/**
 * Xử lý trường amenities để đảm bảo định dạng đúng
 * @param {any} amenities - Dữ liệu amenities từ request
 * @returns {Array|undefined} Mảng các ObjectId hoặc undefined nếu không hợp lệ
 */
const processAmenities = (amenities) => {
  if (!amenities) return undefined;

  try {
    let processedAmenities = amenities;

    // Xử lý trường hợp chuỗi đặc biệt "[ 'string' ]" hoặc tương tự
    if (typeof amenities === 'string') {
      // Loại bỏ các ký tự không cần thiết
      let cleanedString = amenities.replace(/\[|\]|'|"/g, '').trim();

      // Nếu là chuỗi phân cách bởi dấu phẩy
      if (cleanedString.includes(',')) {
        processedAmenities = cleanedString.split(',').map(id => id.trim());
      } else if (cleanedString) {
        // Nếu là một ID duy nhất
        processedAmenities = [cleanedString];
      } else {
        // Nếu chuỗi rỗng sau khi làm sạch
        return undefined;
      }
    }

    // Trường hợp 1: amenities là một chuỗi JSON
    if (typeof amenities === 'string' && (amenities.startsWith('[') || amenities.includes(','))) {
      try {
        const parsed = JSON.parse(amenities);
        if (parsed && (Array.isArray(parsed) || typeof parsed === 'object')) {
          processedAmenities = Array.isArray(parsed) ? parsed : [parsed];
        }
      } catch (jsonError) {
        // Lỗi parse JSON đã được xử lý ở trên
        console.log('Không thể parse JSON, đã xử lý như chuỗi thông thường');
      }
    }

    // Đảm bảo kết quả là một mảng
    if (!Array.isArray(processedAmenities)) {
      processedAmenities = [processedAmenities];
    }

    // Lọc ra các ID hợp lệ (không rỗng và là ObjectId hợp lệ)
    processedAmenities = processedAmenities
      .filter(id => id && id !== '')
      .map(id => {
        // Chuyển đổi thành chuỗi nếu không phải
        const idStr = String(id).trim();
        // Kiểm tra nếu id là ObjectId hợp lệ
        if (mongoose.Types.ObjectId.isValid(idStr)) {
          return idStr;
        }
        console.log(`ID không hợp lệ: ${idStr}`);
        return null;
      })
      .filter(id => id !== null);

    // Kiểm tra nếu mảng rỗng, trả về undefined
    if (processedAmenities.length === 0) {
      return undefined;
    }

    return processedAmenities;
  } catch (error) {
    console.error('Lỗi xử lý amenities:', error);
    return undefined;
  }
};

// @desc    Lấy danh sách phòng trọ
// @route   GET /api/rooms
// @access  Public
const getRooms = async (req, res) => {
  try {
    // Xây dựng query
    const query = { status: { $ne: 'hidden' } };

    // Lọc theo giá
    if (req.query.minPrice || req.query.maxPrice) {
      query.price = {};
      if (req.query.minPrice) query.price.$gte = Number(req.query.minPrice);
      if (req.query.maxPrice) query.price.$lte = Number(req.query.maxPrice);
    }

    // Lọc theo diện tích
    if (req.query.minArea || req.query.maxArea) {
      query.area = {};
      if (req.query.minArea) query.area.$gte = Number(req.query.minArea);
      if (req.query.maxArea) query.area.$lte = Number(req.query.maxArea);
    }

    // Lọc theo địa điểm
    if (req.query.city) query['address.city'] = req.query.city;
    if (req.query.district) query['address.district'] = req.query.district;
    if (req.query.ward) query['address.ward'] = req.query.ward;

    // Lọc theo loại phòng
    if (req.query.category) query.category = req.query.category;

    // Lọc theo trạng thái
    if (req.query.status) query.status = req.query.status;

    // Lọc theo tin nổi bật
    if (req.query.isHighlighted) query.isHighlighted = req.query.isHighlighted === 'true';

    // Phân trang
    const page = parseInt(req.query.page, 10) || 1;
    const limit = Math.min(parseInt(req.query.limit, 10) || 10, 1000); // Giới hạn tối đa 1000 để tránh quá tải
    const startIndex = (page - 1) * limit;

    // Sắp xếp
    let sort = {};
    if (req.query.sort) {
      const sortFields = req.query.sort.split(',');
      for (const field of sortFields) {
        if (field.startsWith('-')) {
          sort[field.substring(1)] = -1;
        } else {
          sort[field] = 1;
        }
      }
    } else {
      // Mặc định sắp xếp theo tin nổi bật và ngày tạo mới nhất
      sort = { isHighlighted: -1, createdAt: -1 };
    }

    // Thực hiện truy vấn
    const rooms = await Room.find(query)
      .populate('category', 'name')
      .populate('amenities', 'name icon')
      .populate('user', 'fullName phone')
      .sort(sort)
      .skip(startIndex)
      .limit(limit);

    // Đếm tổng số phòng trọ
    const total = await Room.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        rooms,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy chi tiết phòng trọ
// @route   GET /api/rooms/:id
// @access  Public
const getRoomById = async (req, res) => {
  try {
    console.log('Query params:', req.query);

    // Kiểm tra xem có yêu cầu tăng lượt xem không
    // Mặc định là tăng lượt xem, trừ khi view=false được truyền vào
    const shouldIncreaseView = req.query.view !== 'false';

    console.log(`Yêu cầu tăng lượt xem: ${shouldIncreaseView}`);

    // Sử dụng findOneAndUpdate để tránh race condition và giảm số lần truy vấn
    const updateQuery = shouldIncreaseView
      ? { $inc: { views: 1 } } // Tăng lượt xem nếu cần
      : {}; // Không thay đổi lượt xem

    const room = await Room.findByIdAndUpdate(
      req.params.id,
      updateQuery,
      { new: true } // Trả về document sau khi cập nhật
    )
      .populate('category', 'name description')
      .populate('amenities', 'name icon')
      .populate('user', 'fullName phone email');

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy phòng trọ'
      });
    }

    // Log lượt xem để debug
    console.log(`Phòng ${room._id} có ${room.views} lượt xem. Đã tăng lượt xem: ${shouldIncreaseView}`);

    res.status(200).json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Lỗi khi lấy chi tiết phòng trọ:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Tạo phòng trọ mới
// @route   POST /api/rooms
// @access  Private
const createRoom = async (req, res) => {
  try {
    console.log('Dữ liệu nhận được:', req.body);
    console.log('Files nhận được:', req.files);

    // Thêm người dùng hiện tại vào dữ liệu
    req.body.user = req.user._id;

    // Xử lý hình ảnh nếu có
    if (req.files && req.files.length > 0) {
      req.body.images = req.files.map(file => `/uploads/${file.filename}`);
      console.log('Đường dẫn hình ảnh:', req.body.images);
    } else {
      console.log('Không có hình ảnh được upload');
    }

    // Xử lý trường address nếu được gửi dưới dạng JSON string
    if (req.body.address && typeof req.body.address === 'string') {
      try {
        req.body.address = JSON.parse(req.body.address);
        console.log('Đã parse address từ JSON string:', req.body.address);
      } catch (err) {
        console.error('Lỗi khi parse address:', err);
        return res.status(400).json({
          success: false,
          message: 'Dữ liệu địa chỉ không hợp lệ',
          error: err.message
        });
      }
    }

    // Xử lý trường amenities
    req.body.amenities = processAmenities(req.body.amenities);
    console.log('Amenities sau khi xử lý:', req.body.amenities);

    // Chuyển đổi các trường số
    if (req.body.price) req.body.price = Number(req.body.price);
    if (req.body.area) req.body.area = Number(req.body.area);
    if (req.body.deposit) req.body.deposit = Number(req.body.deposit);

    console.log('Dữ liệu trước khi tạo phòng:', req.body);

    // Tạo phòng trọ mới
    const room = await Room.create(req.body);
    console.log('Đã tạo phòng thành công:', room._id);

    res.status(201).json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Lỗi tạo phòng:', error);

    // Xử lý lỗi validation
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: 'Dữ liệu không hợp lệ',
        errors: messages
      });
    }

    // Xử lý lỗi CastError
    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: `Dữ liệu không hợp lệ ở trường ${error.path}`,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật phòng trọ
// @route   PUT /api/rooms/:id
// @access  Private
const updateRoom = async (req, res) => {
  try {
    console.log('Dữ liệu cập nhật nhận được:', req.body);
    console.log('Files nhận được:', req.files);

    let room = await Room.findById(req.params.id);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy phòng trọ'
      });
    }

    // Kiểm tra quyền sở hữu
    if (room.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Bạn không có quyền cập nhật phòng trọ này'
      });
    }

    // Xử lý hình ảnh nếu có
    if (req.files && req.files.length > 0) {
      const newImages = req.files.map(file => `/uploads/${file.filename}`);
      console.log('Hình ảnh mới:', newImages);

      // Nếu đã có hình ảnh, thêm vào mảng hiện tại
      if (req.body.keepImages === 'true' && room.images && room.images.length > 0) {
        req.body.images = [...room.images, ...newImages];
      } else {
        req.body.images = newImages;
      }

      console.log('Danh sách hình ảnh sau khi cập nhật:', req.body.images);
    }

    // Xử lý trường address nếu được gửi dưới dạng JSON string
    if (req.body.address && typeof req.body.address === 'string') {
      try {
        req.body.address = JSON.parse(req.body.address);
        console.log('Đã parse address từ JSON string:', req.body.address);
      } catch (err) {
        console.error('Lỗi khi parse address:', err);
        return res.status(400).json({
          success: false,
          message: 'Dữ liệu địa chỉ không hợp lệ',
          error: err.message
        });
      }
    }

    // Xử lý trường amenities
    req.body.amenities = processAmenities(req.body.amenities);
    console.log('Amenities sau khi xử lý:', req.body.amenities);

    // Chuyển đổi các trường số
    if (req.body.price) req.body.price = Number(req.body.price);
    if (req.body.area) req.body.area = Number(req.body.area);
    if (req.body.deposit) req.body.deposit = Number(req.body.deposit);

    console.log('Dữ liệu trước khi cập nhật phòng:', req.body);

    // Cập nhật phòng trọ
    room = await Room.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    console.log('Đã cập nhật phòng thành công:', room._id);

    res.status(200).json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error('Lỗi cập nhật phòng:', error);

    // Xử lý lỗi validation
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: 'Dữ liệu không hợp lệ',
        errors: messages
      });
    }

    // Xử lý lỗi CastError
    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        message: `Dữ liệu không hợp lệ ở trường ${error.path}`,
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }

    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Xóa phòng trọ
// @route   DELETE /api/rooms/:id
// @access  Private
const deleteRoom = async (req, res) => {
  try {
    const room = await Room.findById(req.params.id);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy phòng trọ'
      });
    }

    // Kiểm tra quyền sở hữu
    if (room.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Bạn không có quyền xóa phòng trọ này'
      });
    }

    // Sử dụng deleteOne thay vì remove (đã bị loại bỏ trong Mongoose mới)
    await Room.deleteOne({ _id: req.params.id });

    res.status(200).json({
      success: true,
      message: 'Xóa phòng trọ thành công'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Lấy danh sách phòng trọ của người dùng hiện tại
// @route   GET /api/rooms/my-rooms
// @access  Private
const getMyRooms = async (req, res) => {
  try {
    console.log('Đang lấy danh sách phòng trọ của người dùng:', req.user._id);
    console.log('Query params:', req.query);

    // Xây dựng query
    const query = { user: req.user._id };

    // Lọc theo trạng thái
    if (req.query.status) {
      query.status = req.query.status;
    }

    // Lọc theo từ khóa tìm kiếm
    if (req.query.search) {
      query.$or = [
        { title: { $regex: req.query.search, $options: 'i' } },
        { description: { $regex: req.query.search, $options: 'i' } },
        { 'address.city': { $regex: req.query.search, $options: 'i' } },
        { 'address.district': { $regex: req.query.search, $options: 'i' } }
      ];
    }

    // Phân trang
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 9; // Mặc định 9 phòng/trang cho layout 3x3
    const startIndex = (page - 1) * limit;

    console.log('Query:', query);
    console.log('Pagination:', { page, limit, startIndex });

    // Thực hiện truy vấn
    const rooms = await Room.find(query)
      .populate('category', 'name')
      .populate('amenities', 'name icon')
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);

    // Đếm tổng số phòng trọ
    const total = await Room.countDocuments(query);

    console.log(`Tìm thấy ${rooms.length} phòng trọ, tổng cộng ${total} phòng`);

    res.status(200).json({
      success: true,
      data: {
        rooms,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Lỗi khi lấy danh sách phòng trọ của người dùng:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Cập nhật trạng thái phòng trọ
// @route   PUT /api/rooms/:id/status
// @access  Private
const updateRoomStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!status || !['available', 'rented', 'hidden'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Trạng thái không hợp lệ'
      });
    }

    let room = await Room.findById(req.params.id);

    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy phòng trọ'
      });
    }

    // Kiểm tra quyền sở hữu
    if (room.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Bạn không có quyền cập nhật phòng trọ này'
      });
    }

    // Cập nhật trạng thái
    room.status = status;
    await room.save();

    res.status(200).json({
      success: true,
      data: room
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  getRooms,
  getRoomById,
  createRoom,
  updateRoom,
  deleteRoom,
  getMyRooms,
  updateRoomStatus
};
