import PropTypes from 'prop-types';
import { formatCurrency, formatDate } from '../../utils/format';

/**
 * Component hiển thị tóm tắt thanh toán
 * 
 * @param {Object} props - Props của component
 * @param {Object} props.packageData - Thông tin gói tin nổi bật
 * @param {Object} props.roomData - Thông tin bài đăng
 */
const PaymentSummary = ({ packageData, roomData }) => {
  // Tính ngày hết hạn dựa trên thời hạn gói
  const calculateExpiryDate = () => {
    const today = new Date();
    const expiryDate = new Date(today);
    expiryDate.setDate(today.getDate() + packageData.duration);
    return expiryDate;
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      <div className="p-5 border-b border-gray-200">
        <h3 className="text-lg font-bold mb-1">Tóm tắt thanh toán</h3>
        <p className="text-sm text-gray-500">Vui lòng kiểm tra thông tin trước khi thanh toán</p>
      </div>
      
      <div className="p-5 space-y-4">
        {/* Thông tin bài đăng */}
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Thông tin bài đăng</h4>
          <div className="bg-gray-50 p-3 rounded-md">
            <p className="font-medium">{roomData.title}</p>
            <p className="text-sm text-gray-600 mt-1">
              {roomData.address?.street}, {roomData.address?.ward}, {roomData.address?.district}, {roomData.address?.city}
            </p>
          </div>
        </div>
        
        {/* Thông tin gói */}
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Thông tin gói tin</h4>
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="flex justify-between items-center">
              <p className="font-medium">{packageData.name}</p>
              <span className="text-sm bg-primary text-white px-2 py-1 rounded">
                {packageData.type === 'super_vip' ? 'Super VIP' : packageData.type === 'vip' ? 'VIP' : 'Thường'}
              </span>
            </div>
            <p className="text-sm text-gray-600 mt-1">{packageData.description}</p>
            <p className="text-sm text-gray-600 mt-1">Thời hạn: {packageData.duration} ngày</p>
          </div>
        </div>
        
        {/* Thông tin thời gian */}
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Thời gian hiệu lực</h4>
          <div className="bg-gray-50 p-3 rounded-md grid grid-cols-2 gap-3">
            <div>
              <p className="text-sm text-gray-500">Ngày bắt đầu</p>
              <p className="font-medium">{formatDate(new Date())}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Ngày hết hạn</p>
              <p className="font-medium">{formatDate(calculateExpiryDate())}</p>
            </div>
          </div>
        </div>
        
        {/* Thông tin thanh toán */}
        <div className="pt-3 border-t border-gray-200">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">Giá gói tin</span>
            <span>{formatCurrency(packageData.price)}</span>
          </div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">Thuế VAT (10%)</span>
            <span>{formatCurrency(packageData.price * 0.1)}</span>
          </div>
          <div className="flex justify-between items-center pt-2 border-t border-gray-200 font-bold">
            <span>Tổng thanh toán</span>
            <span className="text-primary">{formatCurrency(packageData.price * 1.1)}</span>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-50 p-4 border-t border-gray-200">
        <p className="text-sm text-gray-500 text-center">
          Bằng việc nhấn "Thanh toán", bạn đồng ý với các điều khoản và điều kiện của chúng tôi.
        </p>
      </div>
    </div>
  );
};

PaymentSummary.propTypes = {
  packageData: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    duration: PropTypes.number.isRequired,
    type: PropTypes.string.isRequired,
  }).isRequired,
  roomData: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    address: PropTypes.shape({
      street: PropTypes.string,
      ward: PropTypes.string,
      district: PropTypes.string,
      city: PropTypes.string,
    }),
  }).isRequired,
};

export default PaymentSummary;
