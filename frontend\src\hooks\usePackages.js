import { useState, useCallback } from 'react';
import { packageService } from '../services';
import PackageModel from '../models/PackageModel';

/**
 * Hook quản lý các thao tác với gói tin nổi bật
 */
const usePackages = () => {
  const [packages, setPackages] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [paymentUrl, setPaymentUrl] = useState('');

  /**
   * Lấy danh sách gói tin nổi bật
   */
  const fetchPackages = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await packageService.getPackages();
      if (response.success && Array.isArray(response.data)) {
        const packagesList = response.data.map(pkg => new PackageModel(pkg));
        setPackages(packagesList);
        return packagesList;
      } else {
        throw new Error(response.message || 'Không thể lấy danh sách gói tin nổi bật')
      }
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách gói tin nổi bật');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Lấy thông tin chi tiết gói tin nổi bật
   * @param {string} id ID của gói tin nổi bật
   */
  const fetchPackageById = useCallback(async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await packageService.getPackageById(id);
      if (response.success && response.data) {
        const packageData = new PackageModel(response.data);
        setSelectedPackage(packageData);
        return packageData;
      } else {
        throw new Error(response.message || 'Không thể lấy thông tin gói tin nổi bật');
      }
    } catch (err) {
      setError(err.message || 'Không thể lấy thông tin gói tin nổi bật');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Tạo yêu cầu thanh toán VNPay
   * @param {string} roomId ID của bài đăng
   * @param {string} packageId ID của gói tin nổi bật
   */
  const createPayment = useCallback(async (roomId, packageId) => {
    setIsLoading(true);
    setError(null);
    setPaymentUrl('');

    try {
      const response = await packageService.createPayment({
        roomId,
        packageId,
        returnUrl: `${window.location.origin}/payment/result`,
      });

      if (response.success && response.data.paymentUrl) {
        setPaymentUrl(response.data.paymentUrl);
        return response.data.paymentUrl;
      } else {
        throw new Error(response.message || 'Không thể tạo yêu cầu thanh toán');
      }
    } catch (err) {
      setError(err.message || 'Không thể tạo yêu cầu thanh toán');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Xác nhận kết quả thanh toán VNPay
   * @param {Object} queryParams Tham số truy vấn từ VNPay
   */
  const verifyPayment = useCallback(async (queryParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await packageService.verifyPayment(queryParams);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể xác nhận kết quả thanh toán');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Nâng cấp bài đăng lên gói tin nổi bật
   * @param {string} roomId ID của bài đăng
   * @param {string} packageId ID của gói tin nổi bật
   */
  const upgradeRoom = useCallback(async (roomId, packageId) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await packageService.upgradeRoom(roomId, packageId);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể nâng cấp bài đăng');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Lấy lịch sử gói tin nổi bật của bài đăng
   * @param {string} roomId ID của bài đăng
   */
  const getRoomPackageHistory = useCallback(async (roomId) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await packageService.getRoomPackageHistory(roomId);
      return response.data;
    } catch (err) {
      setError(err.message || 'Không thể lấy lịch sử gói tin nổi bật');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    packages,
    selectedPackage,
    isLoading,
    error,
    paymentUrl,
    fetchPackages,
    fetchPackageById,
    createPayment,
    verifyPayment,
    upgradeRoom,
    getRoomPackageHistory,
  };
};

export default usePackages;
