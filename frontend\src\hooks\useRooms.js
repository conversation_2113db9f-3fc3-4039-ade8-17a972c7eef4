import { useState, useCallback } from 'react';
import { roomService } from '../services';

// Hook quản lý các thao tác với phòng trọ
const useRooms = () => {
  const [rooms, setRooms] = useState([]);
  const [room, setRoom] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 9, // 9 bài đăng mỗi trang để có layout 3x3 grid
    totalPages: 0,
    totalResults: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Hàm reset room state khi chuyển trang
  const resetRoom = useCallback(() => {
    console.log('useRooms: Resetting room state');
    setRoom(null);
    setError(null);
    setIsLoading(false);
  }, []);

  // <PERSON><PERSON><PERSON>nh s<PERSON>ch phòng trọ
  const fetchRooms = useCallback(async (filters = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await roomService.getRooms(filters);

      if (response.success) {
        setRooms(response.data.rooms);

        // Chuyển đổi cấu trúc pagination từ backend sang frontend
        const backendPagination = response.data.pagination;
        const frontendPagination = {
          page: backendPagination.page,
          limit: backendPagination.limit,
          totalResults: backendPagination.total,
          totalPages: backendPagination.pages
        };
        setPagination(frontendPagination);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách phòng trọ');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Lấy thông tin chi tiết phòng trọ
  const fetchRoomById = useCallback(async (id, options = {}) => {
    // Kiểm tra ID hợp lệ ngay từ đầu
    if (!id || typeof id !== 'string') {
      const errorMsg = 'ID phòng trọ không hợp lệ';
      console.error('useRooms: fetchRoomById -', errorMsg, 'ID:', id);
      setError(errorMsg);
      setIsLoading(false);
      throw new Error(errorMsg);
    }

    console.log(`useRooms: fetchRoomById - Bắt đầu tải phòng trọ ID: ${id}`);
    setIsLoading(true);
    setError(null);
    // Reset room state trước khi fetch
    setRoom(null);

    try {
      // Mặc định tăng lượt xem, trừ khi options.increaseView = false
      const increaseView = options.increaseView !== false;

      console.log(`useRooms: fetchRoomById - Gọi API với ID: ${id}, tăng lượt xem: ${increaseView}`);
      const response = await roomService.getRoomById(id, { view: increaseView });

      console.log('useRooms: fetchRoomById - API response:', response);

      if (!response) {
        throw new Error('Không nhận được phản hồi từ server');
      }

      if (response.success && response.data) {
        // Kiểm tra và đảm bảo các trường quan trọng có giá trị hợp lệ
        const validatedData = {
          ...response.data,
          // Đảm bảo _id tồn tại
          _id: response.data._id || id,
          // Đảm bảo title tồn tại
          title: response.data.title || 'Không có tiêu đề',
          // Đảm bảo images là một mảng
          images: Array.isArray(response.data.images) ? response.data.images : [],
          // Đảm bảo address là một object
          address: typeof response.data.address === 'object' && response.data.address !== null ? response.data.address : {},
          // Đảm bảo user là một object
          user: typeof response.data.user === 'object' && response.data.user !== null ? response.data.user : {},
          // Đảm bảo amenities là một mảng
          amenities: Array.isArray(response.data.amenities) ? response.data.amenities : [],
          // Đảm bảo các trường số
          price: response.data.price || 0,
          area: response.data.area || 0,
          views: response.data.views || 0,
          // Đảm bảo status
          status: response.data.status || 'available'
        };

        console.log('useRooms: fetchRoomById - Dữ liệu đã validate:', validatedData);
        setRoom(validatedData);
        setError(null);
        console.log(`useRooms: fetchRoomById - Thành công! Lượt xem: ${validatedData.views}`);
      } else {
        const errorMsg = response.message || 'Không thể tải thông tin phòng trọ';
        console.error('useRooms: fetchRoomById - API error:', errorMsg, response);
        setError(errorMsg);
        setRoom(null);
      }

      return response;
    } catch (err) {
      console.error('useRooms: fetchRoomById - Exception:', err);
      const errorMessage = err.message || 'Không thể lấy thông tin phòng trọ';
      setError(errorMessage);
      setRoom(null);
      throw err;
    } finally {
      setIsLoading(false);
      console.log('useRooms: fetchRoomById - Hoàn thành');
    }
  }, []);

  // Tạo phòng trọ mới
  const createRoom = async (roomData) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('useRooms: Creating room with data', roomData);
      const response = await roomService.createRoom(roomData);
      console.log('useRooms: Room created successfully', response);
      return response;
    } catch (err) {
      console.error('useRooms: Error creating room', err);
      const errorMessage = err.message || 'Không thể tạo phòng trọ mới. Vui lòng thử lại sau.';
      setError(errorMessage);
      throw { ...err, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  // Cập nhật thông tin phòng trọ
  const updateRoom = async (id, roomData) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('useRooms: Updating room with ID', id, 'and data', roomData);
      const response = await roomService.updateRoom(id, roomData);
      console.log('useRooms: Room updated successfully', response);

      // Cập nhật state nếu đang xem phòng này
      if (room && room._id === id && response.success) {
        setRoom(response.data);
      }

      return response;
    } catch (err) {
      console.error('useRooms: Error updating room', err);
      const errorMessage = err.message || 'Không thể cập nhật thông tin phòng trọ. Vui lòng thử lại sau.';
      setError(errorMessage);
      throw { ...err, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  // Xóa phòng trọ
  const deleteRoom = async (id) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await roomService.deleteRoom(id);
      return response;
    } catch (err) {
      setError(err.message || 'Không thể xóa phòng trọ');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Lấy danh sách phòng trọ của người dùng hiện tại
  const fetchMyRooms = useCallback(async (filters = {}) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await roomService.getMyRooms(filters);
      if (response.success) {
        setRooms(response.data.rooms);

        // Chuyển đổi cấu trúc pagination từ backend sang frontend
        const backendPagination = response.data.pagination;
        const frontendPagination = {
          page: backendPagination.page,
          limit: backendPagination.limit,
          totalResults: backendPagination.total,
          totalPages: backendPagination.pages
        };
        setPagination(frontendPagination);
      }
      return response;
    } catch (err) {
      setError(err.message || 'Không thể lấy danh sách phòng trọ của bạn');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Cập nhật trạng thái phòng trọ
  const updateRoomStatus = async (id, status) => {
    setIsLoading(true);
    setError(null);

    try {
      console.log(`useRooms: Cập nhật trạng thái phòng trọ ID: ${id} thành ${status}`);
      const response = await roomService.updateRoomStatus(id, status);

      // Cập nhật state nếu đang xem phòng này
      if (room && room._id === id && response.success) {
        setRoom({
          ...room,
          status: status
        });
        console.log(`useRooms: Đã cập nhật trạng thái phòng trọ trong state`);
      }

      // Cập nhật danh sách phòng nếu phòng này có trong danh sách
      if (rooms.length > 0 && response.success) {
        const updatedRooms = rooms.map(r =>
          r._id === id ? { ...r, status: status } : r
        );
        setRooms(updatedRooms);
        console.log(`useRooms: Đã cập nhật trạng thái phòng trọ trong danh sách`);
      }

      console.log(`useRooms: Cập nhật trạng thái phòng trọ thành công`, response);
      return response;
    } catch (err) {
      console.error(`useRooms: Lỗi khi cập nhật trạng thái phòng trọ:`, err);
      const errorMessage = err.message || 'Không thể cập nhật trạng thái phòng trọ. Vui lòng thử lại sau.';
      setError(errorMessage);
      throw { ...err, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    rooms,
    room,
    pagination,
    isLoading,
    error,
    fetchRooms,
    fetchRoomById,
    createRoom,
    updateRoom,
    deleteRoom,
    fetchMyRooms,
    updateRoomStatus,
    resetRoom,
  };
};

export default useRooms;
