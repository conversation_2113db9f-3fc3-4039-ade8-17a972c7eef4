import { useEffect, useState, useCallback } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  FaMapMarkerAlt, FaRuler, FaMoneyBillWave, FaPhone, FaUser,
  FaCalendarAlt, FaExpandArrowsAlt, FaTimes, FaChevronLeft,
  FaChevronRight, FaShareAlt, FaCrown, FaHome
} from 'react-icons/fa';
import { useRooms } from '../hooks';
import { useAuthContext } from '../contexts';
import { ImageWithFallback } from '../components';
import { formatCurrency, formatDate } from '../utils/format';
// Xóa import không sử dụng

const RoomDetailPage = () => {
  const { id } = useParams();
  const { fetchRoomById, room, isLoading, error, resetRoom } = useRooms();
  const { isAuthenticated } = useAuthContext();

  // State cho gallery
  const [activeImage, setActiveImage] = useState(0);
  const [isFullScreen, setIsFullScreen] = useState(false);

  // Utility functions để xử lý localStorage an toàn
  const safeGetFromLocalStorage = useCallback((key, defaultValue = null) => {
    try {
      const item = localStorage.getItem(key);
      if (!item || item === 'undefined' || item === 'null') {
        return defaultValue;
      }
      return JSON.parse(item);
    } catch (error) {
      console.error(`RoomDetailPage: Lỗi parse ${key} từ localStorage:`, error);
      // Xóa dữ liệu lỗi
      localStorage.removeItem(key);
      return defaultValue;
    }
  }, []);

  const safeSetToLocalStorage = useCallback((key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`RoomDetailPage: Lỗi lưu ${key} vào localStorage:`, error);
      return false;
    }
  }, []);

  const getCurrentUserId = useCallback(() => {
    const user = safeGetFromLocalStorage('user', null);
    return user?._id || null;
  }, [safeGetFromLocalStorage]);



  // Component bản đồ
  const MapComponent = useCallback(({ address }) => {
    if (!address || typeof address !== 'object') {
      return (
        <div className="h-80 bg-gray-200 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <FaMapMarkerAlt className="text-gray-400 text-4xl mb-2 mx-auto" />
            <p className="text-gray-500">Không có thông tin địa chỉ để hiển thị bản đồ</p>
          </div>
        </div>
      );
    }

    // Tạo địa chỉ đầy đủ để hiển thị
    const fullAddress = [
      address.street,
      address.ward,
      address.district,
      address.city
    ].filter(Boolean).join(', ');

    if (!fullAddress.trim()) {
      return (
        <div className="h-80 bg-gray-200 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <FaMapMarkerAlt className="text-gray-400 text-4xl mb-2 mx-auto" />
            <p className="text-gray-500">Địa chỉ không đầy đủ để hiển thị bản đồ</p>
          </div>
        </div>
      );
    }

    // Sử dụng Google Maps embed (không cần API key cho basic embed)
    const encodedAddress = encodeURIComponent(fullAddress);
    const mapUrl = `https://maps.google.com/maps?q=${encodedAddress}&t=&z=16&ie=UTF8&iwloc=&output=embed`;

    return (
      <div className="h-80 rounded-lg overflow-hidden shadow-sm border">
        <iframe
          src={mapUrl}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen=""
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title={`Bản đồ vị trí: ${fullAddress}`}
          onError={(e) => {
            console.error('Map loading error:', e);
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'flex';
          }}
        />
        <div
          className="h-80 bg-gray-200 rounded-lg items-center justify-center hidden"
          style={{ display: 'none' }}
        >
          <div className="text-center">
            <FaMapMarkerAlt className="text-gray-400 text-4xl mb-2 mx-auto" />
            <p className="text-gray-500">Không thể tải bản đồ</p>
            <p className="text-gray-400 text-sm mt-1">Vui lòng kiểm tra kết nối mạng</p>
            <a
              href={`https://www.google.com/maps/search/?api=1&query=${encodedAddress}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
            >
              Mở trong Google Maps
            </a>
          </div>
        </div>
      </div>
    );
  }, []);

  // Hàm xử lý chuyển ảnh
  const nextImage = useCallback(() => {
    if (room && room.images && Array.isArray(room.images) && room.images.length > 0) {
      setActiveImage((prev) => (prev === room.images.length - 1 ? 0 : prev + 1));
    }
  }, [room]);

  const prevImage = useCallback(() => {
    if (room && room.images && Array.isArray(room.images) && room.images.length > 0) {
      setActiveImage((prev) => (prev === 0 ? room.images.length - 1 : prev - 1));
    }
  }, [room]);

  // Xử lý phím mũi tên để điều hướng ảnh
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (isFullScreen) {
        if (e.key === 'ArrowRight') nextImage();
        if (e.key === 'ArrowLeft') prevImage();
        if (e.key === 'Escape') setIsFullScreen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullScreen, nextImage, prevImage]);



  // Tải thông tin phòng trọ
  useEffect(() => {
    console.log('RoomDetailPage: useEffect triggered with ID:', id);

    // Kiểm tra ID hợp lệ
    if (!id) {
      console.error('RoomDetailPage: ID phòng trọ không hợp lệ');
      toast.error('ID phòng trọ không hợp lệ');
      return;
    }

    // Reset state khi ID thay đổi
    if (resetRoom) {
      resetRoom();
    }

    // Sử dụng localStorage để theo dõi xem người dùng đã xem phòng này chưa
    const viewedRooms = safeGetFromLocalStorage('viewedRooms', {});
    const hasViewedBefore = viewedRooms[id];

    // Chỉ tăng lượt xem nếu người dùng chưa xem phòng này trước đó trong phiên hiện tại
    // Đặt thời gian hết hạn là 1 giờ thay vì 24 giờ để tăng lượt xem chính xác hơn
    const viewExpiry = viewedRooms[id] ? new Date(viewedRooms[id]).getTime() + 60 * 60 * 1000 : 0; // 1 giờ
    const shouldIncreaseView = !hasViewedBefore || Date.now() > viewExpiry;

    const loadRoomDetail = async () => {
      try {
        console.log(`RoomDetailPage: Đang tải thông tin phòng trọ ID: ${id}, tăng lượt xem: ${shouldIncreaseView}`);

        // Gọi API với tham số increaseView rõ ràng
        if (fetchRoomById) {
          const response = await fetchRoomById(id, {
            increaseView: shouldIncreaseView
          });

          console.log('RoomDetailPage: API response received:', response);

          // Lưu ID phòng vào localStorage để đánh dấu đã xem
          if (shouldIncreaseView && response && response.success) {
            viewedRooms[id] = Date.now();
            if (safeSetToLocalStorage('viewedRooms', viewedRooms)) {
              console.log(`RoomDetailPage: Đã đánh dấu phòng ${id} là đã xem`);
            }
          }
        } else {
          console.error('RoomDetailPage: fetchRoomById function not available');
        }
      } catch (err) {
        console.error('RoomDetailPage: Lỗi khi tải thông tin phòng trọ:', err);
        // Không hiển thị toast ở đây vì hook đã xử lý
      }
    };

    // Xóa các phòng đã xem quá 24 giờ
    const cleanupViewedRooms = () => {
      const now = Date.now();
      const oneDayMs = 24 * 60 * 60 * 1000; // 24 giờ tính bằng milliseconds
      let hasChanges = false;

      Object.keys(viewedRooms).forEach(roomId => {
        if (now - viewedRooms[roomId] > oneDayMs) {
          delete viewedRooms[roomId];
          hasChanges = true;
        }
      });

      if (hasChanges) {
        if (safeSetToLocalStorage('viewedRooms', viewedRooms)) {
          console.log('RoomDetailPage: Đã xóa các phòng đã xem quá 24 giờ');
        }
      }
    };

    cleanupViewedRooms();

    // Tải thông tin phòng
    loadRoomDetail();

    // Cleanup function
    return () => {
      console.log('RoomDetailPage: Cleanup component for ID:', id);
    };
  }, [id, fetchRoomById, resetRoom, safeGetFromLocalStorage, safeSetToLocalStorage]); // Dependencies cần thiết



  // Xử lý chia sẻ
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: room?.title || 'Thông tin phòng trọ',
        text: room?.description || 'Xem thông tin phòng trọ này',
        url: window.location.href,
      })
      .catch((error) => console.log('Lỗi khi chia sẻ:', error));
    } else {
      // Fallback khi không hỗ trợ Web Share API
      navigator.clipboard.writeText(window.location.href);
      toast.success('Đã sao chép liên kết vào clipboard');
    }
  };

  // Debug logging
  console.log('RoomDetailPage: Render state:', {
    id,
    isLoading,
    error,
    hasRoom: !!room,
    roomId: room?._id,
    roomTitle: room?.title
  });

  // Hiển thị loading khi đang tải hoặc chưa có dữ liệu
  if (isLoading || (!room && !error)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-primary mx-auto"></div>
          <p className="mt-6 text-gray-600 text-lg">Đang tải thông tin phòng trọ...</p>
          <p className="mt-2 text-gray-500 text-sm">ID: {id}</p>
          <div className="mt-4 text-xs text-gray-400">
            <p>Nếu trang không tải sau 10 giây, vui lòng thử lại</p>
          </div>
        </div>
      </div>
    );
  }

  // Hiển thị lỗi khi có lỗi hoặc không có dữ liệu sau khi load xong
  if (error || (!isLoading && !room)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md p-8 bg-white rounded-lg shadow-md">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Không tìm thấy phòng trọ</h2>
          <p className="mt-2 text-gray-600 mb-6">
            {error || 'Phòng trọ này có thể không tồn tại, đã bị xóa hoặc không còn khả dụng.'}
          </p>
          <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 justify-center">
            <button
              onClick={() => window.location.reload()}
              className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Thử lại
            </button>
            <Link to="/" className="inline-block px-6 py-3 bg-primary text-white rounded-lg font-medium hover:bg-primary-dark transition-colors">
              Trang chủ
            </Link>
            <Link to="/search" className="inline-block px-6 py-3 bg-gray-200 text-gray-800 rounded-lg font-medium hover:bg-gray-300 transition-colors">
              Tìm phòng khác
            </Link>
          </div>
          <div className="mt-6 text-sm text-gray-500 bg-gray-50 p-4 rounded-lg">
            <p><strong>ID phòng:</strong> {id || 'Không có'}</p>
            {error && <p className="mt-1"><strong>Chi tiết lỗi:</strong> {error}</p>}
            <p className="mt-2 text-xs">Nếu bạn tin rằng đây là lỗi hệ thống, vui lòng liên hệ hỗ trợ.</p>
          </div>
        </div>
      </div>
    );
  }

  // Kiểm tra cuối cùng để đảm bảo room có dữ liệu hợp lệ
  if (!room || !room._id) {
    console.error('RoomDetailPage: Room data is invalid:', room);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md p-8 bg-white rounded-lg shadow-md">
          <div className="text-yellow-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Dữ liệu không hợp lệ</h2>
          <p className="mt-2 text-gray-600 mb-6">
            Dữ liệu phòng trọ không đầy đủ hoặc bị lỗi. Vui lòng thử lại sau.
          </p>
          <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 justify-center">
            <button
              onClick={() => window.location.reload()}
              className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Tải lại trang
            </button>
            <Link to="/" className="inline-block px-6 py-3 bg-gray-200 text-gray-800 rounded-lg font-medium hover:bg-gray-300 transition-colors">
              Quay lại trang chủ
            </Link>
          </div>
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 text-xs text-gray-500 bg-gray-50 p-3 rounded">
              <p><strong>Debug Info:</strong></p>
              <p>Room object: {JSON.stringify(room, null, 2)}</p>
              <p>Has room: {!!room ? 'Yes' : 'No'}</p>
              <p>Has room._id: {!!(room && room._id) ? 'Yes' : 'No'}</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Final debug log trước khi render
  if (process.env.NODE_ENV === 'development') {
    console.log('RoomDetailPage: Final render check - Room data:', {
      hasRoom: !!room,
      roomId: room?._id,
      roomTitle: room?.title,
      roomImages: room?.images?.length || 0,
      roomAddress: room?.address,
      roomUser: room?.user,
      roomCategory: room?.category
    });
  }

  return (
    <div className="bg-gray-50 py-8">
      <div className="container-custom">
        {/* Breadcrumb */}
        {/* <nav className="flex mb-6 text-gray-600 text-sm">
          <Link to="/" className="hover:text-primary">Trang chủ</Link>
          <span className="mx-2">/</span>
          <Link to="/search" className="hover:text-primary">Tìm phòng</Link>
          <span className="mx-2">/</span>
          <span className="text-gray-900 font-medium">{room.title}</span>
        </nav> */}

        {/* Tiêu đề và trạng thái */}
        <div className="mb-6">
          <div className="flex flex-wrap items-center justify-between">
            <h1 className="text-2xl md:text-3xl font-bold text-red-500">{room.title}</h1>
            <div className="flex items-center space-x-3">
              <div className="flex space-x-2">
                <div className={`px-3 py-1 rounded-full text-white text-sm font-medium ${
                  room.status === 'available' ? 'bg-green-500' : 'bg-red-500'
                }`}>
                  {room.status === 'available' ? 'Còn phòng' : 'Đã cho thuê'}
                </div>

                {room.isHighlighted && (
                  <div className={`px-3 py-1 rounded-full text-white text-sm font-medium flex items-center ${
                    room.packageType === 'super_vip' ? 'bg-purple-600' :
                    room.packageType === 'vip' ? 'bg-yellow-500' : 'bg-blue-500'
                  }`}>
                    <FaCrown className="mr-1" />
                    {room.packageType === 'super_vip' ? 'Super VIP' :
                     room.packageType === 'vip' ? 'VIP' : 'Nổi bật'}
                  </div>
                )}
              </div>

              <button
                onClick={handleShare}
                className="p-2 rounded-full hover:bg-gray-200 transition-colors"
                title="Chia sẻ"
              >
                <FaShareAlt className="text-gray-500 w-5 h-5" />
              </button>
            </div>
          </div>
          <div className="flex items-center mt-2 text-gray-600">
            <FaMapMarkerAlt className="mr-1" />
            <span>
              {room.address && typeof room.address === 'object' ? (
                `${room.address.street || ''}, ${room.address.ward || ''}, ${room.address.district || ''}, ${room.address.city || ''}`
              ) : (
                'Địa chỉ không có sẵn'
              )}
            </span>
          </div>
        </div>

        {/* Hình ảnh */}
        <div className="mb-8">
          {/* Gallery chính */}
          <div className="relative rounded-xl overflow-hidden shadow-md">
            <div className="relative h-72 md:h-[500px] overflow-hidden">
              {room.images && Array.isArray(room.images) && room.images.length > 0 ? (
                <ImageWithFallback
                  src={room.images[activeImage] || ''}
                  alt={room.title || 'Hình ảnh phòng trọ'}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                  onClick={() => setIsFullScreen(true)}
                  fallbackSrc="https://via.placeholder.com/800x600?text=Lỗi+hình+ảnh"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-200">
                  <p className="text-gray-500">Không có hình ảnh</p>
                </div>
              )}

              {/* Nút điều hướng */}
              {room.images && Array.isArray(room.images) && room.images.length > 1 && (
                <>
                  <button
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                    onClick={(e) => {
                      e.stopPropagation();
                      prevImage();
                    }}
                  >
                    <FaChevronLeft />
                  </button>
                  <button
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                    onClick={(e) => {
                      e.stopPropagation();
                      nextImage();
                    }}
                  >
                    <FaChevronRight />
                  </button>
                </>
              )}

              {/* Nút phóng to */}
              {room.images && Array.isArray(room.images) && room.images.length > 0 && (
                <button
                  className="absolute right-3 bottom-3 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70"
                  onClick={() => setIsFullScreen(true)}
                >
                  <FaExpandArrowsAlt />
                </button>
              )}

              {/* Số lượng ảnh */}
              {room.images && Array.isArray(room.images) && room.images.length > 0 && (
                <div className="absolute left-3 bottom-3 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                  {activeImage + 1}/{room.images.length}
                </div>
              )}
            </div>
          </div>

          {/* Thumbnail */}
          {room.images && Array.isArray(room.images) && room.images.length > 1 && (
            <div className="flex space-x-2 overflow-x-auto mt-4 pb-2">
              {room.images.map((image, index) => (
                <div
                  key={index}
                  className={`w-24 h-24 flex-shrink-0 cursor-pointer rounded-md overflow-hidden border-2 transition-all ${
                    activeImage === index ? 'border-primary scale-105' : 'border-transparent hover:border-gray-300'
                  }`}
                  onClick={() => setActiveImage(index)}
                >
                  <ImageWithFallback
                    src={image || ''}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                    fallbackSrc="https://via.placeholder.com/150x150?text=Lỗi"
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Fullscreen Gallery */}
        {isFullScreen && room.images && Array.isArray(room.images) && room.images.length > 0 && (
          <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
            <button
              className="absolute top-4 right-4 text-white p-2 rounded-full hover:bg-gray-800"
              onClick={() => setIsFullScreen(false)}
            >
              <FaTimes className="w-6 h-6" />
            </button>

            <button
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full hover:bg-gray-800"
              onClick={prevImage}
            >
              <FaChevronLeft className="w-6 h-6" />
            </button>

            <ImageWithFallback
              src={room.images[activeImage] || ''}
              alt={room.title || 'Hình ảnh phòng trọ'}
              className="max-h-[90vh] max-w-[90vw] object-contain"
              fallbackSrc="https://via.placeholder.com/1200x800?text=Lỗi+hình+ảnh"
            />

            <button
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full hover:bg-gray-800"
              onClick={nextImage}
            >
              <FaChevronRight className="w-6 h-6" />
            </button>

            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-4 py-2 rounded-full">
              {activeImage + 1} / {room.images.length}
            </div>
          </div>
        )}

        {/* Thông tin chính */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div className="md:col-span-2">
            {/* Thông tin cơ bản */}
            <div className="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 className="text-xl font-bold mb-6 text-gray-900 border-b pb-3">Thông tin chi tiết</h2>

              <div className="grid grid-cols-2 gap-6">
                <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-4">
                    <FaMoneyBillWave className="text-primary text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Giá thuê</p>
                    <p className="font-semibold text-lg text-gray-900">{room.price ? formatCurrency(room.price) : 'Không có thông tin'} đồng / tháng</p>
                  </div>
                </div>

                <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-4">
                    <FaRuler className="text-primary text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Diện tích</p>
                    <p className="font-semibold text-lg text-gray-900">{room.area ? `${room.area} m²` : 'Không có thông tin'}</p>
                  </div>
                </div>

                <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-4">
                    <FaHome className="text-primary text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Loại phòng</p>
                    <p className="font-semibold text-lg text-gray-900">
                      {room.category && room.category.name ? room.category.name : 'Không có thông tin'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-4">
                    <FaMoneyBillWave className="text-primary text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Tiền cọc</p>
                    <p className="font-semibold text-lg text-gray-900">
                      {room.deposit !== undefined && room.deposit !== null ? (room.deposit > 0 ? `${formatCurrency(room.deposit)} đồng` : 'Không yêu cầu') : 'Không có thông tin'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className="w-12 h-12 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-4">
                    <FaCalendarAlt className="text-primary text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Ngày đăng</p>
                    <p className="font-semibold text-lg text-gray-900">{room.createdAt ? formatDate(room.createdAt) : 'Không có thông tin'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Mô tả */}
            <div className="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 className="text-xl font-bold mb-4 text-gray-900">Mô tả chi tiết</h2>
              <div className="prose prose-lg max-w-none text-gray-700 whitespace-pre-line">
                <p>{room.description || 'Không có mô tả chi tiết'}</p>
              </div>
            </div>

            {/* Tiện nghi */}
            {room.amenities && room.amenities.length > 0 && (
              <div className="bg-white rounded-xl shadow-md p-6 mb-6">
                <h2 className="text-xl font-bold mb-6 text-gray-900 border-b pb-3">Tiện nghi</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                  {room.amenities.map((amenity) => (
                    <div key={amenity._id} className="flex items-center p-3 bg-gray-50 rounded-lg">
                      <div className="w-10 h-10 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-3">
                        <span className="text-primary text-lg">{amenity.icon}</span>
                      </div>
                      <span className="text-gray-800 font-medium">{amenity.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Bản đồ */}
            <div className="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 className="text-xl font-bold mb-4 text-gray-900 flex items-center">
                <FaMapMarkerAlt className="mr-2 text-primary" />
                Vị trí trên bản đồ
              </h2>
              <div className="mb-4">
                <p className="text-gray-600 text-sm">
                  <strong>Địa chỉ:</strong> {room.address && typeof room.address === 'object' ? (
                    `${room.address.street || ''}, ${room.address.ward || ''}, ${room.address.district || ''}, ${room.address.city || ''}`
                  ) : (
                    'Không có thông tin địa chỉ'
                  )}
                </p>
              </div>
              <MapComponent address={room.address} />
              <div className="mt-4 flex flex-wrap gap-2">
                <a
                  href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
                    room.address && typeof room.address === 'object'
                      ? `${room.address.street || ''}, ${room.address.ward || ''}, ${room.address.district || ''}, ${room.address.city || ''}`
                      : room.title || 'Phòng trọ'
                  )}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FaMapMarkerAlt className="mr-1" />
                  Mở trong Google Maps
                </a>
              </div>
            </div>
          </div>

          {/* Thông tin liên hệ */}
          <div>
            <div className="bg-white rounded-xl shadow-md p-6 mb-6 sticky top-4">
              <h2 className="text-xl font-bold mb-4 text-gray-900">Thông tin liên hệ</h2>

              <div className="flex items-center mb-6">
                <div className="w-16 h-16 rounded-full bg-primary bg-opacity-10 flex items-center justify-center mr-4">
                  <FaUser className="text-primary text-2xl" />
                </div>
                <div>
                  <p className="font-semibold text-lg text-gray-900">{room.user && room.user.fullName ? room.user.fullName : 'Không có thông tin'}</p>
                  <p className="text-gray-600">Chủ phòng</p>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4 mb-6 space-y-4">
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <FaPhone className="text-primary mr-3 text-lg" />
                  <span className="text-gray-800 font-medium">{room.user && room.user.phone ? room.user.phone : 'Không có thông tin'}</span>
                </div>
              </div>

              <div className="space-y-3">
                <a
                  href={room.user && room.user.phone ? `tel:${room.user.phone}` : '#'}
                  className="block w-full py-3 px-4 bg-primary text-white text-center rounded-lg font-medium hover:bg-primary-dark transition-colors"
                >
                  Gọi ngay
                </a>

                <button
                  className="block w-full py-3 px-4 bg-green-500 text-white text-center rounded-lg font-medium hover:bg-green-600 transition-colors"
                  onClick={() => room.user && room.user.phone ? window.open(`https://zalo.me/${room.user.phone}`, '_blank') : toast.info('Không có thông tin số điện thoại')}
                >
                  Nhắn Zalo
                </button>

                {isAuthenticated && room.user._id === getCurrentUserId() && (
                  <Link
                    to={`/rooms/upgrade/${room._id}`}
                    className="block w-full py-3 px-4 bg-yellow-500 text-white text-center rounded-lg font-medium hover:bg-yellow-600 transition-colors"
                  >
                    <FaCrown className="inline-block mr-2" />
                    Nâng cấp tin đăng
                  </Link>
                )}
              </div>

              <div className="mt-6 text-center text-gray-500 text-sm">
                <p>Vui lòng cho chủ phòng biết bạn tìm thấy phòng này trên Tìm Phòng Trọ</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomDetailPage;
