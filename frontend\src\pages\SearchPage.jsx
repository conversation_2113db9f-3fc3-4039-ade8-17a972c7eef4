import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaFilter, FaMapMarkerAlt, FaSpinner } from 'react-icons/fa';
import { useRooms, useCategories, useAmenities, useLocations } from '../hooks';
import { Card, FilterPopup } from '../components';
import Pagination from '../components/Pagination';

const SearchPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { fetchRooms, rooms, pagination, isLoading } = useRooms();
  const { fetchCategories, categories } = useCategories();
  const { fetchAmenities, amenities } = useAmenities();
  const {
    provinces,
    districts,
    wards,
    isLoading: isLocationLoading,
    error: locationError,
    fetchProvinces,
    fetchDistrictsByProvince,
    fetchWardsByDistrict
  } = useLocations();

  // L<PERSON>y các tham số tìm kiếm từ URL
  const searchParams = new URLSearchParams(location.search);

  // State cho các bộ lọc
  const [filters, setFilters] = useState({
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    minArea: searchParams.get('minArea') || '',
    maxArea: searchParams.get('maxArea') || '',
    city: searchParams.get('city') || '',
    district: searchParams.get('district') || '',
    ward: searchParams.get('ward') || '',
    category: searchParams.get('category') || '',
    amenities: searchParams.get('amenities') ? searchParams.get('amenities').split(',') : [],
    page: parseInt(searchParams.get('page') || '1', 10),
    limit: 9, // 9 bài đăng mỗi trang để có layout 3x3 grid
  });

  // State cho việc hiển thị popup bộ lọc
  const [showFilterPopup, setShowFilterPopup] = useState(false);

  // Lấy danh sách phòng trọ, loại phòng và tiện nghi khi component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Đảm bảo limit = 9 cho phân trang
        const searchParamsWithLimit = Object.fromEntries(searchParams.entries());
        if (!searchParamsWithLimit.limit) {
          searchParamsWithLimit.limit = 9;
        }

        await Promise.all([
          fetchRooms(searchParamsWithLimit),
          fetchCategories(),
          fetchAmenities(),
          fetchProvinces(),
        ]);
      } catch (err) {
        toast.error('Có lỗi xảy ra khi tải dữ liệu');
      }
    };

    loadData();
  }, [location.search, fetchRooms, fetchCategories, fetchAmenities, fetchProvinces]);

  // Load districts when city changes
  useEffect(() => {
    if (filters.city) {
      const selectedProvince = provinces.find(p => p.name === filters.city);
      if (selectedProvince) {
        fetchDistrictsByProvince(selectedProvince.code);
      }
    }
  }, [filters.city, provinces, fetchDistrictsByProvince]);

  // Load wards when district changes
  useEffect(() => {
    if (filters.city && filters.district) {
      const selectedProvince = provinces.find(p => p.name === filters.city);
      const selectedDistrict = districts.find(d => d.name === filters.district);
      if (selectedProvince && selectedDistrict) {
        fetchWardsByDistrict(selectedProvince.code, selectedDistrict.code);
      }
    }
  }, [filters.city, filters.district, provinces, districts, fetchWardsByDistrict]);

  // Cập nhật URL khi thay đổi bộ lọc
  const updateFilters = (newFilters) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1, limit: 9 }; // Reset về trang 1 và đảm bảo limit = 9
    setFilters(updatedFilters);

    // Tạo URL mới với các tham số tìm kiếm
    const params = new URLSearchParams();

    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value && (Array.isArray(value) ? value.length > 0 : true)) {
        if (Array.isArray(value)) {
          params.set(key, value.join(','));
        } else {
          params.set(key, value);
        }
      }
    });

    navigate(`/search?${params.toString()}`);
  };

  // Xử lý khi thay đổi trang
  const handlePageChange = (newPage) => {
    if (newPage < 1 || newPage > pagination.totalPages) return;

    const updatedFilters = { ...filters, page: newPage, limit: 9 }; // Đảm bảo limit = 9
    setFilters(updatedFilters);

    const params = new URLSearchParams(location.search);
    params.set('page', newPage);
    params.set('limit', 9); // Đảm bảo limit = 9 trong URL
    navigate(`/search?${params.toString()}`);
  };



  // Xử lý khi xóa tất cả bộ lọc
  const clearAllFilters = () => {
    const clearedFilters = {
      minPrice: '',
      maxPrice: '',
      minArea: '',
      maxArea: '',
      city: '',
      district: '',
      ward: '',
      category: '',
      amenities: [],
      page: 1,
      limit: 9, // Đảm bảo limit = 9
    };
    setFilters(clearedFilters);

    // Cập nhật URL về trạng thái mặc định
    navigate('/search');
  };

  return (
    <div className="bg-background dark:bg-background-dark min-h-screen py-8">
      <div className="container-custom">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Bộ lọc (desktop) */}
          <div className="hidden md:block w-64 flex-shrink-0">
            <div className="bg-card dark:bg-card-dark rounded-lg shadow-md sticky top-4 max-h-[calc(100vh-2rem)] overflow-hidden flex flex-col">
              {/* Fixed Header */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <h2 className="text-lg font-bold text-text dark:text-text-dark">Bộ lọc tìm kiếm</h2>
              </div>

              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto custom-scrollbar min-h-0 p-4"
                   style={{ scrollbarWidth: 'thin', scrollbarColor: 'rgba(156, 163, 175, 0.5) transparent' }}>

              {/* Khoảng giá */}
              <div className="mb-4">
                <h3 className="font-medium mb-2 text-text dark:text-text-dark">Khoảng giá</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <input
                      type="number"
                      placeholder="Từ"
                      className="form-input text-sm py-1"
                      value={filters.minPrice}
                      onChange={(e) => setFilters({ ...filters, minPrice: e.target.value })}
                    />
                  </div>
                  <div>
                    <input
                      type="number"
                      placeholder="Đến"
                      className="form-input text-sm py-1"
                      value={filters.maxPrice}
                      onChange={(e) => setFilters({ ...filters, maxPrice: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              {/* Diện tích */}
              <div className="mb-4">
                <h3 className="font-medium mb-2 text-text dark:text-text-dark">Diện tích (m²)</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <input
                      type="number"
                      placeholder="Từ"
                      className="form-input text-sm py-1"
                      value={filters.minArea}
                      onChange={(e) => setFilters({ ...filters, minArea: e.target.value })}
                    />
                  </div>
                  <div>
                    <input
                      type="number"
                      placeholder="Đến"
                      className="form-input text-sm py-1"
                      value={filters.maxArea}
                      onChange={(e) => setFilters({ ...filters, maxArea: e.target.value })}
                    />
                  </div>
                </div>
              </div>

              {/* Loại phòng */}
              <div className="mb-4">
                <h3 className="font-medium mb-2 text-text dark:text-text-dark">Loại phòng</h3>
                <select
                  className="form-input text-sm py-1"
                  value={filters.category}
                  onChange={(e) => setFilters({ ...filters, category: e.target.value })}
                >
                  <option value="">Tất cả loại phòng</option>
                  {categories.map((category) => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Lọc theo khu vực */}
              <div className="mb-4">
                <h3 className="flex items-center font-medium mb-2 text-text dark:text-text-dark">
                  <FaMapMarkerAlt className="mr-2 text-primary" />
                  Khu vực
                </h3>

                {/* Error message */}
                {locationError && (
                  <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-xs text-red-600">{locationError}</p>
                  </div>
                )}

                <div className="space-y-2">
                  {/* Tỉnh/Thành phố */}
                  <div className="relative">
                    <select
                      className="form-input text-sm py-1 w-full pr-8"
                      value={filters.city}
                      onChange={(e) => setFilters({ ...filters, city: e.target.value, district: '', ward: '' })}
                      disabled={isLocationLoading}
                    >
                      <option value="">Chọn tỉnh/thành phố</option>
                      {provinces.map((province) => (
                        <option key={province.code} value={province.name}>
                          {province.name}
                        </option>
                      ))}
                    </select>
                    {isLocationLoading && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                        <FaSpinner className="animate-spin text-gray-400 text-xs" />
                      </div>
                    )}
                  </div>

                  {/* Quận/Huyện */}
                  <div className="relative">
                    <select
                      className="form-input text-sm py-1 w-full pr-8"
                      value={filters.district}
                      onChange={(e) => setFilters({ ...filters, district: e.target.value, ward: '' })}
                      disabled={!filters.city || isLocationLoading}
                    >
                      <option value="">
                        {filters.city ? 'Chọn quận/huyện' : 'Chọn tỉnh/thành phố trước'}
                      </option>
                      {districts.map((district) => (
                        <option key={district.code} value={district.name}>
                          {district.name}
                        </option>
                      ))}
                    </select>
                    {isLocationLoading && filters.city && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                        <FaSpinner className="animate-spin text-gray-400 text-xs" />
                      </div>
                    )}
                  </div>

                  {/* Phường/Xã */}
                  <div className="relative">
                    <select
                      className="form-input text-sm py-1 w-full pr-8"
                      value={filters.ward}
                      onChange={(e) => setFilters({ ...filters, ward: e.target.value })}
                      disabled={!filters.district || isLocationLoading}
                    >
                      <option value="">
                        {filters.district ? 'Chọn phường/xã' : 'Chọn quận/huyện trước'}
                      </option>
                      {wards.map((ward) => (
                        <option key={ward.code} value={ward.name}>
                          {ward.name}
                        </option>
                      ))}
                    </select>
                    {isLocationLoading && filters.district && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                        <FaSpinner className="animate-spin text-gray-400 text-xs" />
                      </div>
                    )}
                  </div>

                  {/* Selected address display */}
                  {(filters.city || filters.district || filters.ward) && (
                    <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                      <p className="text-xs text-blue-700">
                        <strong>Địa chỉ:</strong>{' '}
                        {[filters.ward, filters.district, filters.city]
                          .filter(Boolean)
                          .join(', ')}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Tiện nghi */}
              <div className="mb-4">
                <h3 className="font-medium mb-2 text-text dark:text-text-dark">Tiện nghi</h3>
                <div className="space-y-2">
                  {amenities.map((amenity) => (
                    <div key={amenity._id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`amenity-${amenity._id}`}
                        className="h-4 w-4 text-primary focus:ring-primary border-dark-300 rounded dark:border-dark-600"
                        checked={filters.amenities.includes(amenity._id)}
                        onChange={(e) => {
                          const updatedAmenities = e.target.checked
                            ? [...filters.amenities, amenity._id]
                            : filters.amenities.filter((id) => id !== amenity._id);
                          setFilters({ ...filters, amenities: updatedAmenities });
                        }}
                      />
                      <label
                        htmlFor={`amenity-${amenity._id}`}
                        className="ml-2 block text-sm text-text dark:text-text-dark"
                      >
                        {amenity.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <button
                  className="w-full py-2 px-4 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  onClick={() => updateFilters(filters)}
                >
                  Áp dụng bộ lọc
                </button>

                <button
                  className="w-full py-2 px-4 border border-gray-300 text-gray-700 dark:text-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  onClick={clearAllFilters}
                >
                  Xóa bộ lọc
                </button>
              </div>
              </div>
            </div>
          </div>

          {/* Kết quả tìm kiếm */}
          <div className="flex-1">
            {/* Header kết quả tìm kiếm */}
            <div className="bg-card dark:bg-card-dark rounded-lg shadow-md p-4 mb-6">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-text dark:text-text-dark">
                  Kết quả tìm kiếm
                </h2>

                <button
                  className="md:hidden bg-primary text-white px-3 py-2 rounded-md hover:bg-primary-dark transition-colors"
                  onClick={() => setShowFilterPopup(true)}
                  aria-label="Mở bộ lọc"
                >
                  <FaFilter className="mr-2" />
                  Bộ lọc
                </button>
              </div>
            </div>

            {/* Kết quả */}
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
                <p className="mt-4 text-dark-600 dark:text-dark-400">Đang tải kết quả...</p>
              </div>
            ) : rooms.length > 0 ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {rooms.map((room) => (
                    <Card key={room._id} room={room} />
                  ))}
                </div>

                {/* Component Pagination */}
                {pagination && pagination.totalPages > 1 && (
                  <Pagination
                    currentPage={filters.page}
                    totalPages={pagination.totalPages}
                    onPageChange={handlePageChange}
                  />
                )}
              </>
            ) : (
              <div className="text-center py-12 bg-card dark:bg-card-dark rounded-lg shadow-md">
                <p className="text-xl font-medium text-text dark:text-text-dark">Không tìm thấy kết quả nào</p>
                <p className="mt-2 text-dark-600 dark:text-dark-400">
                  Vui lòng thử lại với các tiêu chí tìm kiếm khác
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Popup bộ lọc */}
      <FilterPopup
        isOpen={showFilterPopup}
        onClose={() => setShowFilterPopup(false)}
        categories={categories}
        filters={filters}
        onApplyFilters={updateFilters}
      />
    </div>
  );
};

export default SearchPage;
