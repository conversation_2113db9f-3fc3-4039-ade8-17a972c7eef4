import { useState, useEffect } from 'react';
import { Fa<PERSON><PERSON>ch, Fa<PERSON>rash, <PERSON>a<PERSON><PERSON>, FaExclamation<PERSON>riangle, FaFilter } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useAuthContext } from '../../contexts';
import { formatCurrency, formatDate } from '../../utils/format';
import Pagination from '../../components/Pagination';
import Modal from '../../components/Modal';

const RoomManagement = () => {
  const { token } = useAuthContext();
  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('');
  const [isHighlighted, setIsHighlighted] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Modals
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [showFiltersModal, setShowFiltersModal] = useState(false);

  // Advanced filters
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [minArea, setMinArea] = useState('');
  const [maxArea, setMaxArea] = useState('');
  const [city, setCity] = useState('');

  // Fetch rooms
  const fetchRooms = async () => {
    try {
      setLoading(true);

      // Build query params
      const params = new URLSearchParams();
      params.append('page', pagination.page);
      params.append('limit', pagination.limit);
      if (search) params.append('search', search);
      if (status) params.append('status', status);
      if (isHighlighted !== '') params.append('isHighlighted', isHighlighted);
      if (minPrice) params.append('minPrice', minPrice);
      if (maxPrice) params.append('maxPrice', maxPrice);
      if (minArea) params.append('minArea', minArea);
      if (maxArea) params.append('maxArea', maxArea);
      if (city) params.append('city', city);

      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/rooms?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể lấy danh sách phòng trọ');
      }

      setRooms(data.data.rooms);
      setPagination({
        page: data.data.pagination.page,
        limit: data.data.pagination.limit,
        total: data.data.pagination.total,
        pages: data.data.pagination.pages
      });
    } catch (error) {
      console.error('Lỗi khi lấy danh sách phòng trọ:', error);
      setError(error.message);
      toast.error(`Lỗi: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRooms();
  }, [token, pagination.page, pagination.limit]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchRooms();
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Apply filters
  const applyFilters = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    setShowFiltersModal(false);
    fetchRooms();
  };

  // Reset filters
  const resetFilters = () => {
    setSearch('');
    setStatus('');
    setIsHighlighted('');
    setMinPrice('');
    setMaxPrice('');
    setMinArea('');
    setMaxArea('');
    setCity('');
    setPagination(prev => ({ ...prev, page: 1 }));
    setShowFiltersModal(false);
    fetchRooms();
  };

  // Handle delete
  const openDeleteModal = (room) => {
    setSelectedRoom(room);
    setShowDeleteModal(true);
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/admin/rooms/${selectedRoom._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Không thể xóa phòng trọ');
      }

      // Remove room from list
      setRooms(rooms.filter(room => room._id !== selectedRoom._id));
      toast.success(`Đã xóa phòng trọ "${selectedRoom.title}"`);
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Lỗi khi xóa phòng trọ:', error);
      toast.error(`Lỗi: ${error.message}`);
    }
  };

  // View room details
  const viewRoomDetails = (roomId) => {
    window.open(`/rooms/${roomId}`, '_blank');
  };

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <FaExclamationTriangle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">
              Đã xảy ra lỗi khi tải dữ liệu
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Quản lý bài đăng phòng trọ</h1>
      </div>

      {/* Basic Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <form onSubmit={handleSearch} className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Tìm kiếm theo tiêu đề..."
              className="input input-bordered w-full pr-10"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
            <button type="submit" className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <FaSearch className="text-gray-400" />
            </button>
          </div>

          <div>
            <select
              className="select select-bordered w-full"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
            >
              <option value="">Tất cả trạng thái</option>
              <option value="available">Còn trống</option>
              <option value="rented">Đã cho thuê</option>
              <option value="hidden">Đã ẩn</option>
            </select>
          </div>

          <div>
            <select
              className="select select-bordered w-full"
              value={isHighlighted}
              onChange={(e) => setIsHighlighted(e.target.value)}
            >
              <option value="">Tất cả tin</option>
              <option value="true">Tin nổi bật</option>
              <option value="false">Tin thường</option>
            </select>
          </div>

          <div className="flex space-x-2">
            <button type="submit" className="btn btn-primary flex-1">
              Lọc
            </button>
            <button
              type="button"
              onClick={() => setShowFiltersModal(true)}
              className="btn btn-outline"
              title="Bộ lọc nâng cao"
            >
              <FaFilter />
            </button>
          </div>
        </form>
      </div>

      {/* Rooms table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bài đăng
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Giá & Diện tích
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Địa chỉ
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Người đăng
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
                    </div>
                  </td>
                </tr>
              ) : rooms.length === 0 ? (
                <tr>
                  <td colSpan="6" className="px-6 py-4 text-center text-sm text-gray-500">
                    Không tìm thấy phòng trọ nào
                  </td>
                </tr>
              ) : (
                rooms.map((room) => (
                  <tr key={room._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          {room.images && room.images.length > 0 ? (
                            <img
                              className="h-10 w-10 rounded-md object-cover"
                              src={room.images[0]}
                              alt={room.title}
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.style.display = 'none';
                                e.target.parentNode.innerHTML = `<div class="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center text-gray-500">Ảnh</div>`;
                              }}
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-md bg-gray-200 flex items-center justify-center text-gray-500">
                              <FaHome className="h-5 w-5" />
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 line-clamp-1">
                            {room.title}
                            {room.isHighlighted && (
                              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                VIP
                              </span>
                            )}
                          </div>
                          <div className="text-xs text-gray-500">
                            Đăng ngày: {formatDate(room.createdAt)}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 font-medium">
                        {formatCurrency(room.price)}/tháng
                      </div>
                      <div className="text-sm text-gray-500">
                        {room.area} m²
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-500 line-clamp-2">
                        {room.address?.district}, {room.address?.city}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        room.status === 'available' ? 'bg-green-100 text-green-800' :
                        room.status === 'rented' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {room.status === 'available' ? 'Còn trống' :
                         room.status === 'rented' ? 'Đã cho thuê' : 'Đã ẩn'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {room.user?.fullName || 'Không có thông tin'}
                      </div>
                      <div className="text-xs text-gray-500">
                        {room.user?.email || ''}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => viewRoomDetails(room._id)}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="Xem chi tiết"
                        >
                          <FaEye />
                        </button>
                        <button
                          onClick={() => openDeleteModal(room)}
                          className="text-red-600 hover:text-red-900"
                          title="Xóa"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && rooms.length > 0 && (
          <div className="px-6 py-4 bg-white border-t border-gray-200">
            <Pagination
              currentPage={pagination.page}
              totalPages={pagination.pages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* Delete Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Xác nhận xóa phòng trọ"
      >
        <div className="p-6">
          <p className="text-sm text-gray-500">
            Bạn có chắc chắn muốn xóa phòng trọ <span className="font-semibold">"{selectedRoom?.title}"</span>?
            Hành động này không thể hoàn tác.
          </p>
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="btn btn-outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Hủy
            </button>
            <button
              type="button"
              className="btn btn-error"
              onClick={handleDelete}
            >
              Xóa
            </button>
          </div>
        </div>
      </Modal>

      {/* Advanced Filters Modal */}
      <Modal
        isOpen={showFiltersModal}
        onClose={() => setShowFiltersModal(false)}
        title="Bộ lọc nâng cao"
      >
        <div className="p-6">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Giá tối thiểu
                </label>
                <input
                  type="number"
                  className="input input-bordered w-full"
                  placeholder="VNĐ"
                  value={minPrice}
                  onChange={(e) => setMinPrice(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Giá tối đa
                </label>
                <input
                  type="number"
                  className="input input-bordered w-full"
                  placeholder="VNĐ"
                  value={maxPrice}
                  onChange={(e) => setMaxPrice(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Diện tích tối thiểu
                </label>
                <input
                  type="number"
                  className="input input-bordered w-full"
                  placeholder="m²"
                  value={minArea}
                  onChange={(e) => setMinArea(e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Diện tích tối đa
                </label>
                <input
                  type="number"
                  className="input input-bordered w-full"
                  placeholder="m²"
                  value={maxArea}
                  onChange={(e) => setMaxArea(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tỉnh/Thành phố
              </label>
              <input
                type="text"
                className="input input-bordered w-full"
                placeholder="Nhập tên tỉnh/thành phố"
                value={city}
                onChange={(e) => setCity(e.target.value)}
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="btn btn-outline"
              onClick={resetFilters}
            >
              Đặt lại
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={applyFilters}
            >
              Áp dụng
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default RoomManagement;
